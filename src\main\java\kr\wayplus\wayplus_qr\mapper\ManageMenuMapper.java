package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.dto.MenuDto;
import kr.wayplus.wayplus_qr.entity.MenuEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

@Mapper
public interface ManageMenuMapper {

    // ========== 메뉴 관리 ==========

    /**
     * 메뉴를 생성합니다.
     */
    int insertMenu(MenuEntity.Menu menu);

    /**
     * 메뉴 정보를 수정합니다.
     */
    int updateMenu(MenuEntity.Menu menu);

    /**
     * 메뉴를 논리 삭제합니다.
     */
    int deleteMenu(@Param("menuId") Long menuId, @Param("deleteUserEmail") String deleteUserEmail);

    /**
     * 메뉴 ID로 메뉴 정보를 조회합니다.
     */
    Optional<MenuEntity.Menu> selectMenuById(@Param("menuId") Long menuId);

    /**
     * 메뉴 코드로 메뉴 정보를 조회합니다.
     */
    Optional<MenuEntity.Menu> selectMenuByCode(@Param("menuCode") String menuCode);

    /**
     * 모든 활성 메뉴 목록을 조회합니다.
     */
    List<MenuDto.Response> selectAllActiveMenus();

    /**
     * 모든 메뉴 목록을 조회합니다.
     */
    List<MenuDto.Response> selectAllMenus();

    /**
     * 특정 사용자가 접근 가능한 메뉴 트리를 조회합니다.
     */
    List<MenuDto.Response> selectAccessibleMenuTree(@Param("userEmail") String userEmail, @Param("roleId") String roleId);

    /**
     * 상위 메뉴 ID로 하위 메뉴 목록을 조회합니다.
     */
    List<MenuDto.Response> selectMenusByParentId(@Param("parentMenuId") Long parentMenuId);

    /**
     * 메뉴 코드 중복 여부를 확인합니다.
     */
    boolean existsByMenuCode(@Param("menuCode") String menuCode, @Param("excludeMenuId") Long excludeMenuId);

    /**
     * 메뉴 이름 중복 여부를 확인합니다.
     */
    boolean existsByMenuName(@Param("menuName") String menuName, @Param("excludeMenuId") Long excludeMenuId);

    /**
     * 메뉴 URL 중복 여부를 확인합니다.
     */
    boolean existsByMenuUrl(@Param("menuUrl") String menuUrl, @Param("excludeMenuId") Long excludeMenuId);

    /**
     * 특정 메뉴에 하위 메뉴가 있는지 확인합니다.
     */
    boolean hasChildMenus(@Param("menuId") Long menuId);

    /**
     * 메뉴 레벨별 최대 표시 순서를 조회합니다.
     */
    Integer selectMaxDisplayOrder(@Param("parentMenuId") Long parentMenuId, @Param("menuLevel") Integer menuLevel);

    // ========== 역할별 권한 관리 ==========

    /**
     * 메뉴 역할 권한을 생성합니다.
     */
    int insertMenuRolePermission(MenuEntity.MenuRolePermission menuRolePermission);

    /**
     * 메뉴 역할 권한을 수정합니다.
     */
    int updateMenuRolePermission(MenuEntity.MenuRolePermission menuRolePermission);

    /**
     * 메뉴 역할 권한을 논리 삭제합니다.
     */
    int deleteMenuRolePermission(@Param("permissionId") Long permissionId, @Param("deleteUserEmail") String deleteUserEmail);

    /**
     * 메뉴와 역할로 권한 정보를 조회합니다.
     */
    Optional<MenuEntity.MenuRolePermission> selectRolePermissionByMenuIdAndRoleId(@Param("menuId") Long menuId, @Param("roleId") String roleId);

    /**
     * 특정 메뉴의 모든 역할 권한을 조회합니다.
     */
    List<MenuDto.PermissionResponse.RolePermission> selectRolePermissionsByMenuId(@Param("menuId") Long menuId);

    /**
     * 특정 역할이 특정 메뉴에 접근 가능한지 확인합니다.
     */
    boolean isAccessibleByRole(@Param("menuId") Long menuId, @Param("roleId") String roleId);

    /**
     * 특정 메뉴의 모든 역할 권한을 삭제합니다.
     */
    int deleteAllRolePermissionsByMenuId(@Param("menuId") Long menuId, @Param("deleteUserEmail") String deleteUserEmail);

    // ========== 사용자별 권한 관리 ==========

    /**
     * 메뉴 사용자 권한을 생성합니다.
     */
    int insertMenuUserPermission(MenuEntity.MenuUserPermission menuUserPermission);

    /**
     * 메뉴 사용자 권한을 수정합니다.
     */
    int updateMenuUserPermission(MenuEntity.MenuUserPermission menuUserPermission);

    /**
     * 메뉴 사용자 권한을 논리 삭제합니다.
     */
    int deleteMenuUserPermission(@Param("userPermissionId") Long userPermissionId, @Param("deleteUserEmail") String deleteUserEmail);

    /**
     * 메뉴와 사용자로 권한 정보를 조회합니다.
     */
    Optional<MenuEntity.MenuUserPermission> selectUserPermissionByMenuIdAndUserEmail(@Param("menuId") Long menuId, @Param("userEmail") String userEmail);

    /**
     * 특정 메뉴의 모든 사용자 권한을 조회합니다.
     */
    List<MenuDto.PermissionResponse.UserPermission> selectUserPermissionsByMenuId(@Param("menuId") Long menuId);

    /**
     * 특정 사용자가 특정 메뉴에 접근 가능한지 확인합니다.
     */
    boolean isAccessibleByUser(@Param("menuId") Long menuId, @Param("userEmail") String userEmail);

    /**
     * 특정 메뉴의 모든 사용자 권한을 삭제합니다.
     */
    int deleteAllUserPermissionsByMenuId(@Param("menuId") Long menuId, @Param("deleteUserEmail") String deleteUserEmail);

    // ========== 권한 체크 관련 메서드 ==========

    /**
     * 메뉴 코드로 메뉴 ID를 조회합니다.
     */
    Optional<Long> selectMenuIdByMenuCode(@Param("menuCode") String menuCode);

    /**
     * 사용자의 특정 메뉴에 대한 CRUD 권한을 체크합니다.
     * 사용자별 권한 우선, 없으면 역할별 권한 체크
     */
    boolean hasUserPermission(@Param("menuCode") String menuCode, @Param("userEmail") String userEmail, @Param("roleId") String roleId, @Param("permissionType") String permissionType);

    /**
     * 특정 메뉴의 모든 사용자 권한을 상세 조회합니다 (CRUD 권한 포함).
     */
    List<MenuDto.PermissionResponse.UserPermission> selectDetailedUserPermissionsByMenuId(@Param("menuId") Long menuId);

    /**
     * SUPER_ADMIN을 제외한 모든 역할 목록을 조회합니다.
     */
    List<MenuDto.PermissionResponse.RolePermission> selectAllRolesExceptSuperAdmin();

    /**
     * SUPER_ADMIN을 포함한 모든 역할 목록을 조회합니다.
     */
    List<MenuDto.PermissionResponse.RolePermission> selectAllRoles();

}
