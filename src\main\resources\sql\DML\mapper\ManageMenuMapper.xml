<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.ManageMenuMapper">

    <!-- ========== ResultMaps ========== -->

    <!-- Menu 엔티티 ResultMap -->
    <resultMap id="menuResultMap" type="kr.wayplus.wayplus_qr.entity.MenuEntity$Menu">
        <id property="menuId" column="menu_id"/>
        <result property="parentMenuId" column="parent_menu_id"/>
        <result property="menuCode" column="menu_code"/>
        <result property="menuName" column="menu_name"/>
        <result property="menuUrl" column="menu_url"/>
        <result property="menuIcon" column="menu_icon"/>
        <result property="menuLevel" column="menu_level"/>
        <result property="displayOrder" column="display_order"/>
        <result property="status" column="status"/>
        <result property="createUserEmail" column="create_user_email"/>
        <result property="createDate" column="create_date"/>
        <result property="updateUserEmail" column="update_user_email"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="deleteUserEmail" column="delete_user_email"/>
        <result property="deleteDate" column="delete_date"/>
        <result property="useYn" column="use_yn"/>
        <result property="deleteYn" column="delete_yn"/>
    </resultMap>

    <!-- MenuRolePermission 엔티티 ResultMap -->
    <resultMap id="menuRolePermissionResultMap" type="kr.wayplus.wayplus_qr.entity.MenuEntity$MenuRolePermission">
        <id property="permissionId" column="permission_id"/>
        <result property="menuId" column="menu_id"/>
        <result property="roleId" column="role_id"/>
        <result property="isAccessible" column="is_accessible"/>
        <result property="createUserEmail" column="create_user_email"/>
        <result property="createDate" column="create_date"/>
        <result property="updateUserEmail" column="update_user_email"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="deleteUserEmail" column="delete_user_email"/>
        <result property="deleteDate" column="delete_date"/>
        <result property="useYn" column="use_yn"/>
        <result property="deleteYn" column="delete_yn"/>
    </resultMap>

    <!-- MenuUserPermission 엔티티 ResultMap -->
    <resultMap id="menuUserPermissionResultMap" type="kr.wayplus.wayplus_qr.entity.MenuEntity$MenuUserPermission">
        <id property="userPermissionId" column="user_permission_id"/>
        <result property="menuId" column="menu_id"/>
        <result property="userEmail" column="user_email"/>
        <result property="isAccessible" column="is_accessible"/>
        <result property="canRead" column="can_read"/>
        <result property="canWrite" column="can_write"/>
        <result property="canUpdate" column="can_update"/>
        <result property="canDelete" column="can_delete"/>
        <result property="permissionNote" column="permission_note"/>
        <result property="createUserEmail" column="create_user_email"/>
        <result property="createDate" column="create_date"/>
        <result property="updateUserEmail" column="update_user_email"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="deleteUserEmail" column="delete_user_email"/>
        <result property="deleteDate" column="delete_date"/>
        <result property="useYn" column="use_yn"/>
        <result property="deleteYn" column="delete_yn"/>
    </resultMap>

    <!-- MenuDto.Response ResultMap -->
    <resultMap id="menuResponseResultMap" type="kr.wayplus.wayplus_qr.dto.MenuDto$Response">
        <id property="menuId" column="menu_id"/>
        <result property="parentMenuId" column="parent_menu_id"/>
        <result property="menuCode" column="menu_code"/>
        <result property="menuName" column="menu_name"/>
        <result property="menuUrl" column="menu_url"/>
        <result property="menuIcon" column="menu_icon"/>
        <result property="menuLevel" column="menu_level"/>
        <result property="displayOrder" column="display_order"/>
        <result property="status" column="status"/>
        <result property="createDate" column="create_date"/>
        <result property="accessible" column="menu_accessible"/>
    </resultMap>

    <!-- RolePermission DTO ResultMap -->
    <resultMap id="rolePermissionDtoResultMap" type="kr.wayplus.wayplus_qr.dto.MenuDto$PermissionResponse$RolePermission">
        <result property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="isAccessible" column="is_accessible"/>
        <result property="createDate" column="create_date"/>
    </resultMap>

    <!-- UserPermission DTO ResultMap -->
    <resultMap id="userPermissionDtoResultMap" type="kr.wayplus.wayplus_qr.dto.MenuDto$PermissionResponse$UserPermission">
        <result property="userEmail" column="user_email"/>
        <result property="userName" column="name"/>
        <result property="isAccessible" column="is_accessible"/>
        <result property="permissionNote" column="permission_note"/>
        <result property="createDate" column="create_date"/>
    </resultMap>

    <!-- ========== 메뉴 관리 쿼리 ========== -->

    <!-- 메뉴 생성 -->
    <insert id="insertMenu" parameterType="kr.wayplus.wayplus_qr.entity.MenuEntity$Menu" useGeneratedKeys="true" keyProperty="menuId">
        INSERT INTO manage_menus (
            parent_menu_id, menu_code, menu_name, menu_url, menu_icon,
            menu_level, display_order, status,
            create_user_email, create_date, update_user_email, last_update_date,
            use_yn, delete_yn
        ) VALUES (
            #{parentMenuId}, #{menuCode}, #{menuName}, #{menuUrl}, #{menuIcon},
            #{menuLevel}, #{displayOrder}, #{status},
            #{createUserEmail}, #{createDate}, #{updateUserEmail}, #{lastUpdateDate},
            #{useYn}, #{deleteYn}
        )
    </insert>

    <!-- 메뉴 수정 -->
    <update id="updateMenu" parameterType="kr.wayplus.wayplus_qr.entity.MenuEntity$Menu">
        UPDATE manage_menus
        SET parent_menu_id = #{parentMenuId},
            menu_name = #{menuName},
            menu_url = #{menuUrl},
            menu_icon = #{menuIcon},
            menu_level = #{menuLevel},
            display_order = #{displayOrder},
            status = #{status},
            update_user_email = #{updateUserEmail},
            last_update_date = #{lastUpdateDate}
        WHERE menu_id = #{menuId}
          AND delete_yn = 'N'
    </update>

    <!-- 메뉴 논리 삭제 -->
    <update id="deleteMenu">
        UPDATE manage_menus
        SET delete_yn = 'Y',
            delete_user_email = #{deleteUserEmail},
            delete_date = NOW()
        WHERE menu_id = #{menuId}
          AND delete_yn = 'N'
    </update>

    <!-- 메뉴 ID로 조회 -->
    <select id="selectMenuById" resultMap="menuResultMap">
        SELECT *
        FROM manage_menus
        WHERE menu_id = #{menuId}
          AND delete_yn = 'N'
    </select>

    <!-- 메뉴 코드로 조회 -->
    <select id="selectMenuByCode" resultMap="menuResultMap">
        SELECT *
        FROM manage_menus
        WHERE menu_code = #{menuCode}
          AND delete_yn = 'N'
    </select>

    <!-- 모든 메뉴 조회 (ACTIVE, INACTIVE 모두 포함) -->
    <select id="selectAllMenus" resultMap="menuResponseResultMap">
        SELECT menu_id, parent_menu_id, menu_code, menu_name, menu_url, menu_icon,
               menu_level, display_order, status, create_date,
               CASE WHEN status = 'ACTIVE' THEN true ELSE false END AS menu_accessible
        FROM manage_menus
        WHERE delete_yn = 'N'
        ORDER BY menu_level, display_order, menu_id
    </select>

    <!-- 모든 활성 메뉴 조회 -->
    <select id="selectAllActiveMenus" resultMap="menuResponseResultMap">
        SELECT menu_id, parent_menu_id, menu_code, menu_name, menu_url, menu_icon,
               menu_level, display_order, status, create_date,
               CASE WHEN status = 'ACTIVE' THEN true ELSE false END AS menu_accessible
        FROM manage_menus
        WHERE status = 'ACTIVE'
          AND delete_yn = 'N'
        ORDER BY menu_level, display_order, menu_id
    </select>

    <!-- 접근 가능한 메뉴 트리 조회 -->
    <select id="selectAccessibleMenuTree" resultMap="menuResponseResultMap">
        SELECT DISTINCT m.menu_id, m.parent_menu_id, m.menu_code, m.menu_name, m.menu_url, m.menu_icon,
               m.menu_level, m.display_order, m.status, m.create_date,
               CASE WHEN m.status = 'ACTIVE' AND (
                   COALESCE(mup.is_accessible, mrp.is_accessible, 'N') = 'Y'
               ) THEN true ELSE false END AS menu_accessible
        FROM manage_menus m
        LEFT JOIN manage_menu_role_permissions mrp ON m.menu_id = mrp.menu_id
            AND mrp.role_id = #{roleId} AND mrp.delete_yn = 'N' AND mrp.use_yn = 'Y'
        LEFT JOIN manage_menu_user_permissions mup ON m.menu_id = mup.menu_id
            AND mup.user_email = #{userEmail} AND mup.delete_yn = 'N' AND mup.use_yn = 'Y'
        WHERE m.delete_yn = 'N'
          AND (COALESCE(mup.is_accessible, mrp.is_accessible, 'N') = 'Y')
        ORDER BY m.menu_level, m.display_order, m.menu_id
    </select>

    <!-- 상위 메뉴 ID로 하위 메뉴 조회 -->
    <select id="selectMenusByParentId" resultMap="menuResponseResultMap">
        SELECT menu_id, parent_menu_id, menu_code, menu_name, menu_url, menu_icon,
               menu_level, display_order, status, create_date,
               CASE WHEN status = 'ACTIVE' THEN true ELSE false END AS menu_accessible
        FROM manage_menus
        WHERE parent_menu_id = #{parentMenuId}
          AND delete_yn = 'N'
        ORDER BY display_order, menu_id
    </select>

    <!-- 메뉴 코드 중복 확인 -->
    <select id="existsByMenuCode" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM manage_menus
        WHERE menu_code = #{menuCode}
          AND delete_yn = 'N'
        <if test="excludeMenuId != null">
          AND menu_id != #{excludeMenuId}
        </if>
    </select>

    <!-- 메뉴 이름 중복 확인 -->
    <select id="existsByMenuName" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM manage_menus
        WHERE menu_name = #{menuName}
          AND delete_yn = 'N'
        <if test="excludeMenuId != null">
          AND menu_id != #{excludeMenuId}
        </if>
    </select>

    <!-- 메뉴 URL 중복 확인 -->
    <select id="existsByMenuUrl" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM manage_menus
        WHERE menu_url = #{menuUrl}
          AND menu_url IS NOT NULL
          AND menu_url != ''
          AND delete_yn = 'N'
        <if test="excludeMenuId != null">
          AND menu_id != #{excludeMenuId}
        </if>
    </select>

    <!-- 하위 메뉴 존재 확인 -->
    <select id="hasChildMenus" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM manage_menus
        WHERE parent_menu_id = #{menuId}
          AND delete_yn = 'N'
    </select>

    <!-- 최대 표시 순서 조회 -->
    <select id="selectMaxDisplayOrder" resultType="Integer">
        SELECT COALESCE(MAX(display_order), 0)
        FROM manage_menus
        WHERE delete_yn = 'N'
        <if test="parentMenuId != null">
          AND parent_menu_id = #{parentMenuId}
        </if>
        <if test="parentMenuId == null">
          AND parent_menu_id IS NULL
        </if>
          AND menu_level = #{menuLevel}
    </select>

    <!-- ========== 역할별 권한 관리 쿼리 ========== -->

    <!-- 메뉴 역할 권한 생성 -->
    <insert id="insertMenuRolePermission" parameterType="kr.wayplus.wayplus_qr.entity.MenuEntity$MenuRolePermission" useGeneratedKeys="true" keyProperty="permissionId">
        INSERT INTO manage_menu_role_permissions (
            menu_id, role_id, is_accessible,
            create_user_email, create_date, update_user_email, last_update_date,
            use_yn, delete_yn
        ) VALUES (
            #{menuId}, #{roleId}, #{isAccessible},
            #{createUserEmail}, #{createDate}, #{updateUserEmail}, #{lastUpdateDate},
            #{useYn}, #{deleteYn}
        )
    </insert>

    <!-- 메뉴 역할 권한 수정 -->
    <update id="updateMenuRolePermission" parameterType="kr.wayplus.wayplus_qr.entity.MenuEntity$MenuRolePermission">
        UPDATE manage_menu_role_permissions
        SET is_accessible = #{isAccessible},
            update_user_email = #{updateUserEmail},
            last_update_date = #{lastUpdateDate}
        WHERE permission_id = #{permissionId}
          AND delete_yn = 'N'
    </update>

    <!-- 메뉴 역할 권한 논리 삭제 -->
    <update id="deleteMenuRolePermission">
        UPDATE manage_menu_role_permissions
        SET delete_yn = 'Y',
            delete_user_email = #{deleteUserEmail},
            delete_date = NOW()
        WHERE permission_id = #{permissionId}
          AND delete_yn = 'N'
    </update>

    <!-- 메뉴와 역할로 권한 조회 -->
    <select id="selectRolePermissionByMenuIdAndRoleId" resultMap="menuRolePermissionResultMap">
        SELECT *
        FROM manage_menu_role_permissions
        WHERE menu_id = #{menuId}
          AND role_id = #{roleId}
          AND delete_yn = 'N'
    </select>

    <!-- 특정 메뉴의 모든 역할 권한 조회 -->
    <select id="selectRolePermissionsByMenuId" resultMap="rolePermissionDtoResultMap">
        SELECT mrp.role_id, r.role_name, mrp.is_accessible, mrp.create_date
        FROM manage_menu_role_permissions mrp
        LEFT JOIN roles r ON mrp.role_id = r.role_id
        WHERE mrp.menu_id = #{menuId}
          AND mrp.delete_yn = 'N'
          AND mrp.use_yn = 'Y'
        ORDER BY mrp.role_id
    </select>

    <!-- 역할별 메뉴 접근 가능 여부 확인 -->
    <select id="isAccessibleByRole" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM manage_menu_role_permissions
        WHERE menu_id = #{menuId}
          AND role_id = #{roleId}
          AND is_accessible = 'Y'
          AND delete_yn = 'N'
          AND use_yn = 'Y'
    </select>

    <!-- 특정 메뉴의 모든 역할 권한 삭제 -->
    <update id="deleteAllRolePermissionsByMenuId">
        UPDATE manage_menu_role_permissions
        SET delete_yn = 'Y',
            delete_user_email = #{deleteUserEmail},
            delete_date = NOW()
        WHERE menu_id = #{menuId}
          AND delete_yn = 'N'
    </update>

    <!-- ========== 사용자별 권한 관리 쿼리 ========== -->

    <!-- 메뉴 사용자 권한 생성 -->
    <insert id="insertMenuUserPermission" parameterType="kr.wayplus.wayplus_qr.entity.MenuEntity$MenuUserPermission" useGeneratedKeys="true" keyProperty="userPermissionId">
        INSERT INTO manage_menu_user_permissions (
            menu_id, user_email, is_accessible, can_read, can_write, can_update, can_delete, permission_note,
            create_user_email, create_date, update_user_email, last_update_date,
            use_yn, delete_yn
        ) VALUES (
            #{menuId}, #{userEmail}, #{isAccessible}, #{canRead}, #{canWrite}, #{canUpdate}, #{canDelete}, #{permissionNote},
            #{createUserEmail}, #{createDate}, #{updateUserEmail}, #{lastUpdateDate},
            #{useYn}, #{deleteYn}
        )
    </insert>

    <!-- 메뉴 사용자 권한 수정 -->
    <update id="updateMenuUserPermission" parameterType="kr.wayplus.wayplus_qr.entity.MenuEntity$MenuUserPermission">
        UPDATE manage_menu_user_permissions
        SET is_accessible = #{isAccessible},
            can_read = #{canRead},
            can_write = #{canWrite},
            can_update = #{canUpdate},
            can_delete = #{canDelete},
            permission_note = #{permissionNote},
            update_user_email = #{updateUserEmail},
            last_update_date = #{lastUpdateDate}
        WHERE user_permission_id = #{userPermissionId}
          AND delete_yn = 'N'
    </update>

    <!-- 메뉴 사용자 권한 논리 삭제 -->
    <update id="deleteMenuUserPermission">
        UPDATE manage_menu_user_permissions
        SET delete_yn = 'Y',
            delete_user_email = #{deleteUserEmail},
            delete_date = NOW()
        WHERE user_permission_id = #{userPermissionId}
          AND delete_yn = 'N'
    </update>

    <!-- 메뉴와 사용자로 권한 조회 -->
    <select id="selectUserPermissionByMenuIdAndUserEmail" resultMap="menuUserPermissionResultMap">
        SELECT *
        FROM manage_menu_user_permissions
        WHERE menu_id = #{menuId}
          AND user_email = #{userEmail}
          AND delete_yn = 'N'
    </select>

    <!-- 특정 메뉴의 모든 사용자 권한 조회 -->
    <select id="selectUserPermissionsByMenuId" resultMap="userPermissionDtoResultMap">
        SELECT mup.user_email, u.name as name,
               mup.is_accessible, mup.permission_note, mup.create_date
        FROM manage_menu_user_permissions mup
        LEFT JOIN users u ON mup.user_email = u.user_email
        WHERE mup.menu_id = #{menuId}
          AND mup.delete_yn = 'N'
          AND mup.use_yn = 'Y'
        ORDER BY mup.user_email
    </select>

    <!-- 사용자별 메뉴 접근 가능 여부 확인 -->
    <select id="isAccessibleByUser" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM manage_menu_user_permissions
        WHERE menu_id = #{menuId}
          AND user_email = #{userEmail}
          AND is_accessible = 'Y'
          AND delete_yn = 'N'
          AND use_yn = 'Y'
    </select>

    <!-- 특정 메뉴의 모든 사용자 권한 삭제 -->
    <update id="deleteAllUserPermissionsByMenuId">
        UPDATE manage_menu_user_permissions
        SET delete_yn = 'Y',
            delete_user_email = #{deleteUserEmail},
            delete_date = NOW()
        WHERE menu_id = #{menuId}
          AND delete_yn = 'N'
    </update>

    <!-- ========== 권한 체크 관련 쿼리 ========== -->

    <!-- 메뉴 코드로 메뉴 ID 조회 -->
    <select id="selectMenuIdByMenuCode" resultType="java.lang.Long">
        SELECT menu_id
        FROM manage_menus
        WHERE menu_code = #{menuCode}
          AND delete_yn = 'N'
          AND use_yn = 'Y'
        LIMIT 1
    </select>

    <!-- 사용자의 특정 메뉴에 대한 CRUD 권한 체크 -->
    <select id="hasUserPermission" resultType="boolean">
        SELECT
            CASE
                WHEN #{permissionType} = 'READ' THEN COALESCE(ump.can_read, 'N') = 'Y'
                WHEN #{permissionType} = 'WRITE' THEN COALESCE(ump.can_write, 'N') = 'Y'
                WHEN #{permissionType} = 'UPDATE' THEN COALESCE(ump.can_update, 'N') = 'Y'
                WHEN #{permissionType} = 'DELETE' THEN COALESCE(ump.can_delete, 'N') = 'Y'
                ELSE FALSE
            END as hasPermission
        FROM manage_menus m
        LEFT JOIN manage_menu_user_permissions ump ON m.menu_id = ump.menu_id
            AND ump.user_email = #{userEmail}
            AND ump.is_accessible = 'Y'
            AND ump.delete_yn = 'N'
            AND ump.use_yn = 'Y'
        WHERE m.menu_code = #{menuCode}
          AND m.delete_yn = 'N'
          AND m.use_yn = 'Y'
        LIMIT 1
    </select>

    <!-- 특정 메뉴의 모든 사용자 권한을 상세 조회 (CRUD 권한 포함) -->
    <select id="selectDetailedUserPermissionsByMenuId" resultType="kr.wayplus.wayplus_qr.dto.MenuDto$PermissionResponse$UserPermission">
        SELECT
            ump.user_email as userEmail,
            u.name as userName,
            ump.is_accessible as isAccessible,
            ump.can_read as canRead,
            ump.can_write as canWrite,
            ump.can_update as canUpdate,
            ump.can_delete as canDelete,
            ump.permission_note as permissionNote,
            ump.create_date as createDate
        FROM manage_menu_user_permissions ump
        LEFT JOIN users u ON ump.user_email = u.user_email AND u.delete_yn = 'N'
        WHERE ump.menu_id = #{menuId}
          AND ump.delete_yn = 'N'
          AND ump.use_yn = 'Y'
        ORDER BY ump.create_date DESC
    </select>

    <!-- SUPER_ADMIN을 제외한 모든 역할 목록 조회 -->
    <select id="selectAllRolesExceptSuperAdmin" resultType="kr.wayplus.wayplus_qr.dto.MenuDto$PermissionResponse$RolePermission">
        SELECT
            r.role_id as roleId,
            CASE
                WHEN r.role_name LIKE '%관리자' THEN SUBSTRING_INDEX(r.role_name, ' - ', -1)
                WHEN r.role_name LIKE '%뷰어' THEN SUBSTRING_INDEX(r.role_name, ' - ', -1)
                ELSE r.role_name
            END as roleName,
            'N' as isAccessible,
            NOW() as createDate
        FROM roles r
        WHERE r.role_id != 'SUPER_ADMIN'
        ORDER BY
            CASE r.role_id
                WHEN 'PROJECT_ADMIN' THEN 1
                WHEN 'SUB_ADMIN' THEN 2
                WHEN 'VIEWER' THEN 3
                ELSE 4
            END
    </select>

    <!-- SUPER_ADMIN을 포함한 모든 역할 목록 조회 -->
    <select id="selectAllRoles" resultType="kr.wayplus.wayplus_qr.dto.MenuDto$PermissionResponse$RolePermission">
        SELECT
            r.role_id as roleId,
            CASE
                WHEN r.role_name LIKE '%관리자' THEN SUBSTRING_INDEX(r.role_name, ' - ', -1)
                WHEN r.role_name LIKE '%뷰어' THEN SUBSTRING_INDEX(r.role_name, ' - ', -1)
                ELSE r.role_name
            END as roleName,
            'Y' as isAccessible,
            NOW() as createDate
        FROM roles r
        ORDER BY
            CASE r.role_id
                WHEN 'SUPER_ADMIN' THEN 0
                WHEN 'PROJECT_ADMIN' THEN 1
                WHEN 'SUB_ADMIN' THEN 2
                WHEN 'VIEWER' THEN 3
                ELSE 4
            END
    </select>

</mapper>
